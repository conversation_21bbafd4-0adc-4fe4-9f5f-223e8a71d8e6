// AudioRecorder.tsx
import { useCallback, useEffect, useRef, useState } from 'react';
import { Audio } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Circle } from 'react-native-svg';
import { Y<PERSON><PERSON>ck, XStack, Button, Text, styled } from 'tamagui';
import { Mic, Play, Pause, StopCircle, Trash2 } from '@tamagui/lucide-icons';
import { Animated, Easing } from 'react-native';
import { useUpdateMicrophonePermission } from '@/hooks/usePermissions';

export interface AudioRecorderProps {
    onFileChange: (uri: string | null) => void;
}

/* ───── constants ───── */
const MAX_MS = 5_000;
const SIZE   = 84;     // ↓ smaller
const STROKE = 6;
const BORDER = 2;
const RADIUS = (SIZE - STROKE) / 2;
const CIRC   = 2 * Math.PI * RADIUS;

/* gradient ring with yellow outline */
const Ring = styled(LinearGradient, {
    name: 'Ring',
    width: SIZE + BORDER * 2,
    height: SIZE + BORDER * 2,
    borderRadius: (SIZE + BORDER * 2) / 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: BORDER,
    borderColor: '#fed900',
}) as any;

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

export default function AudioRecorder({ onFileChange }: AudioRecorderProps) {
    /* refs & state */
    const rec   = useRef<Audio.Recording | null>(null);
    const snd   = useRef<Audio.Sound | null>(null);
    const tick  = useRef<NodeJS.Timeout | null>(null);
    const sweep = useRef(new Animated.Value(0)).current;

    const [mode, setMode] = useState<'idle' | 'rec' | 'play'>('idle');
    const [uri,  setUri]  = useState<string | null>(null);
    const [ms,   setMs]   = useState(0);

    /* helpers */
    const fmt   = (t:number)=>`0:${`${Math.floor(t/1000)}`.padStart(2,'0')}`;
    const clear = () => { if (tick.current) clearInterval(tick.current); };

    /* recording */
    const start = useCallback(async () => {
        const { granted } = await Audio.requestPermissionsAsync();
        if (!granted) return;

        await Audio.setAudioModeAsync({ allowsRecordingIOS:true, playsInSilentModeIOS:true });
        const r = new Audio.Recording();
        await r.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
        await r.startAsync();

        rec.current = r;
        setMode('rec');
        setMs(0);
        sweep.setValue(0);

        Animated.timing(sweep,{toValue:1,duration:MAX_MS,easing:Easing.linear,useNativeDriver:false}).start();

        tick.current = setInterval(async () => {
            const s = await r.getStatusAsync();
            setMs(s.durationMillis ?? 0);
            if ((s.durationMillis ?? 0) >= MAX_MS) stop();
        },200);
    },[]);

    const stop = useCallback(async () => {
        if (!rec.current) return;
        clear();
        await rec.current.stopAndUnloadAsync();
        const local = rec.current.getURI()!;
        rec.current = null;

        const { sound } = await Audio.Sound.createAsync(
            { uri: local },
            { shouldPlay:false, volume:1.0 }
        );
        snd.current = sound;

        setUri(local);
        onFileChange(local);
        setMs(MAX_MS);
        setMode('idle');
    },[onFileChange]);

    /* playback */
    const togglePlay = useCallback(async () => {
        if (!snd.current) return;
        if (mode==='play'){
            await snd.current.pauseAsync();
            setMode('idle');
        } else {
            await snd.current.replayAsync();
            setMode('play');
        }
    },[mode]);

    snd.current?.setOnPlaybackStatusUpdate(st=>{
        if(!st.isLoaded) return;
        setMs(st.positionMillis);
        if(st.didJustFinish) setMode('idle');
    });

    /* cleanup */
    useEffect(()=>()=>{ clear(); rec.current?.stopAndUnloadAsync(); snd.current?.unloadAsync(); },[]);

    /* visuals */
    const dash = sweep.interpolate({ inputRange:[0,1], outputRange:[CIRC,0] });
    const Icon =
        mode==='rec'? StopCircle :
            mode==='play'? Pause :
                uri? Play : Mic;

    /* UI */
    return (
        <YStack gap="$2">
            <XStack ai="center" gap="$3">
                {/* action ring */}
                <Ring
                    colors={ mode==='rec'? ['#ff6157','#ff9357'] :
                        mode==='play'? ['#34d97b','#20c7c9'] : ['#7d7dff','#b17dff'] }
                    start={{x:0,y:0}} end={{x:1,y:1}}
                    pressStyle={{scale:0.94}}
                    onPress={ mode==='rec'? stop : uri? togglePlay : start }
                >
                    <Svg width={SIZE} height={SIZE} style={{position:'absolute'}}>
                        <AnimatedCircle
                            stroke="#ffffff55"
                            strokeWidth={STROKE}
                            cx={SIZE/2}
                            cy={SIZE/2}
                            r={RADIUS}
                            strokeDasharray={`${CIRC},${CIRC}`}
                            strokeDashoffset={dash}
                            strokeLinecap="round"
                        />
                    </Svg>
                    <Icon color="white" size={26}/>
                </Ring>

                {/* status label left of delete */}
                <Text fs="$6" color="$gray11" minWidth={80}>
                    {mode==='rec'
                        ? `Rec ${fmt(ms)}`
                        : mode==='play'
                            ? `Play ${fmt(ms)}`
                            : uri
                                ? 'Ready • 0:05'
                                : 'Tap to record'}
                </Text>

                {/* delete in red again */}
                {uri && (
                    <Button
                        circular
                        size="$3"           // smaller
                        bg="$red10"
                        color="$gray1"
                        icon={Trash2}
                        pressStyle={{scale:0.92}}
                        onPress={()=>{
                            snd.current?.unloadAsync();
                            snd.current=null;
                            setUri(null); setMs(0); setMode('idle'); sweep.setValue(0);
                            onFileChange(null);
                        }}
                    />
                )}
            </XStack>
        </YStack>
    );
}
