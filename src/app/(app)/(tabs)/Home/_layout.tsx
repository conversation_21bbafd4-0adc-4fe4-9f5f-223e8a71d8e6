import {Stack} from 'expo-router';
import BackButton from "@/components/BackButton";

export default function HomeLayout() {
    return (
        <Stack
            initialRouteName='HomeScreen'
            screenOptions={{headerShown: false}}
        >
            <Stack.Screen
                name="MoodboardScreen"
                options={{
                    headerShown: true,
                    headerTitle: 'Moodboard',
                    headerLeft: () => <BackButton/>,
                }}

            />
        </Stack>
    );
}
