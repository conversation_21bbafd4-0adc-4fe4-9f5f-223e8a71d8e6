import React, { useState, useRef, useEffect } from 'react';
import { 
    Stack, 
    YStack, 
    XStack, 
    Input, 
    ScrollView, 
    Image, 
    Text,
    Button 
} from 'tamagui';
import { 
    TouchableOpacity, 
    Dimensions, 
    KeyboardAvoidingView,
    Platform,
    Keyboard,
    StatusBar
} from 'react-native';
import { useStickersStore } from '@/stores/useStickers';
import { searchGifsTenor, getFeaturedGifsTenor } from '@/api/searchAPI';
import { Sticker } from '@/components/Sticker';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import {useHideTabBar} from "@/hooks/useHideTabBar";

// Get screen dimensions
const { width: screenWidth } = Dimensions.get('window');
const CANVAS_PADDING = 20;
const CANVAS_SIZE = screenWidth - (CANVAS_PADDING * 2);
const SEARCH_HEIGHT = 350;

interface TenorGif {
    id: string;
    title: string;
    content_description: string;
    itemurl: string;
    url: string;
    hasaudio: boolean;
    media_formats: {
        gif?: {
            url: string;
            dims: [number, number];
            size: number;
        };
        tinygif?: {
            url: string;
            dims: [number, number];
            size: number;
        };
        mediumgif?: {
            url: string;
            dims: [number, number];
            size: number;
        };
        nanogif?: {
            url: string;
            dims: [number, number];
            size: number;
        };
    };
}

export default function MoodboardScreen() {
    const stickers = useStickersStore((state) => state.stickers);
    const addSticker = useStickersStore((state) => state.addSticker);
    const removeSticker = useStickersStore((state) => state.removeSticker);
    const updateStickerTransform = useStickersStore((state) => state.updateStickerTransform);
    const bringToFront = useStickersStore((state) => state.bringToFront);
    useHideTabBar();

    const [query, setQuery] = useState('');
    const [results, setResults] = useState<TenorGif[]>([]);
    const [loading, setLoading] = useState(false);
    const [showSearch, setShowSearch] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [offset, setOffset] = useState(0);
    const [hasMore, setHasMore] = useState(false);
    const [nextToken, setNextToken] = useState<string>('');

    const cacheRef = useRef<Record<string, TenorGif[]>>({});
    const searchTimeoutRef = useRef<NodeJS.Timeout>();
    const [featuredLoaded, setFeaturedLoaded] = useState(false);

    // Load featured GIFs when search opens
    const loadFeaturedGifs = async () => {
        if (!showSearch || featuredLoaded) return;
        
        try {
            setLoading(true);
            const data = await getFeaturedGifsTenor();
            const gifs = data.results || [];
            setResults(gifs);
            setFeaturedLoaded(true);
            // Featured GIFs don't support pagination
            setHasMore(false);
        } catch (error) {
            console.error('Error loading featured GIFs:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle search
    const handleSearch = async (searchQuery: string, isNewSearch = true) => {
        if (isNewSearch) {
            setQuery(searchQuery);
            setOffset(0);
            setResults([]);
            setNextToken('');
        }

        if (!searchQuery || searchQuery.length <= 2) {
            setResults([]);
            setHasMore(false);
            return;
        }

        // Clear previous timeout
        if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
        }

        // Debounce search
        searchTimeoutRef.current = setTimeout(async () => {
            try {
                setLoading(true);
                const currentOffset = isNewSearch ? 0 : offset;
                const data = await searchGifsTenor(searchQuery, currentOffset, 20, 'en_US');
                const gifs = data.results || [];
                
                if (isNewSearch) {
                    setResults(gifs);
                } else {
                    setResults(prev => [...prev, ...gifs]);
                }
                
                // Check if there are more results to load
                const moreAvailable = gifs.length === 20 || !!data.next;
                setHasMore(moreAvailable);
                setOffset(currentOffset + gifs.length);
                setNextToken(data.next || '');
                console.log('Search complete:', { 
                    gifsLoaded: gifs.length, 
                    totalResults: currentOffset + gifs.length,
                    hasMore: moreAvailable,
                    nextToken: data.next 
                });
            } catch (error) {
                console.error('Search error:', error);
            } finally {
                setLoading(false);
            }
        }, 300);
    };

    // Load more GIFs
    const loadMore = () => {
        if (!loading && hasMore && query && query.length > 2) {
            handleSearch(query, false);
        }
    };

    // Handle selecting a GIF
    const handleSelectGif = (gif: TenorGif) => {
        const gifUrl = gif.media_formats?.gif?.url || gif.media_formats?.mediumgif?.url || '';
        
        // Generate unique ID for each sticker instance
        const uniqueId = `${gif.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Add to store with centered position and some randomness
        const randomOffset = () => (Math.random() - 0.5) * 100; // -50 to +50
        addSticker({ 
            id: uniqueId, 
            uri: gifUrl,
            position: { 
                x: CANVAS_SIZE / 2 - 60 + randomOffset(),
                y: CANVAS_SIZE / 2 - 60 + randomOffset()
            }
        });

        // Keep search open but dismiss keyboard
        Keyboard.dismiss();
    };

    // Handle canvas tap
    const handleCanvasTap = () => {
        if (editMode) {
            setEditMode(false);
        }
    };

    // Load featured GIFs when search panel opens
    useEffect(() => {
        if (showSearch) {
            loadFeaturedGifs();
        }
    }, [showSearch]);

    return (
        <Stack flex={1}>
            <StatusBar barStyle="light-content" />
            <KeyboardAvoidingView 
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}
            >
                <Stack flex={1}>
                        {/* Canvas - adjusted position */}
                        <Stack flex={1} justifyContent="center" alignItems="center" paddingHorizontal="$4" paddingTop="$4" paddingBottom="$1">
                            <TouchableOpacity 
                                activeOpacity={1} 
                                onPress={handleCanvasTap}
                                style={{
                                    width: CANVAS_SIZE,
                                    height: CANVAS_SIZE,
                                    backgroundColor: '#0a0a0a',
                                    borderRadius: 20,
                                    borderWidth: 2,
                                    borderColor: 'rgba(255,255,255,0.08)',
                                    overflow: 'hidden',
                                }}
                            >
                                {stickers.length === 0 && !showSearch && (
                                    <YStack 
                                        flex={1} 
                                        justifyContent="center" 
                                        alignItems="center"
                                    >
                                        <TouchableOpacity
                                            onPress={() => setShowSearch(true)}
                                            style={{
                                                padding: 20,
                                                borderRadius: 16,
                                                backgroundColor: 'rgba(255,255,255,0.05)',
                                            }}
                                        >
                                            <Ionicons name="add" size={48} color="rgba(255,255,255,0.3)" />
                                        </TouchableOpacity>
                                    </YStack>
                                )}
                                
                                {[...stickers]
                                    .sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0))
                                    .map((sticker) => (
                                        <Sticker 
                                            key={sticker.id} 
                                            id={sticker.id}
                                            uri={sticker.uri}
                                            initialPosition={sticker.position}
                                            initialScale={sticker.scale}
                                            initialRotation={sticker.rotation}
                                            zIndex={sticker.zIndex || 0}
                                            editMode={editMode}
                                            onDelete={() => removeSticker(sticker.id)}
                                            onTransformEnd={(transform) => updateStickerTransform(sticker.id, transform)}
                                            onPress={() => bringToFront(sticker.id)}
                                            onLongPress={() => setEditMode(true)}
                                        />
                                    ))}
                            </TouchableOpacity>
                        </Stack>

                        {/* Search Panel - More prominent */}
                        <YStack 
                            backgroundColor="#0a0a0a" 
                            borderTopLeftRadius={24}
                            borderTopRightRadius={24}
                            height={showSearch ? SEARCH_HEIGHT : 100}
                            padding="$4"
                            borderTopWidth={2}
                            borderTopColor="rgba(255,255,255,0.15)"
                            shadowColor="#000"
                            shadowOffset={{ width: 0, height: -2 }}
                            shadowOpacity={0.25}
                            shadowRadius={3.84}
                            elevation={5}
                        >
                            <XStack alignItems="center" marginBottom={showSearch ? "$3" : 0} gap="$2">
                                {!showSearch ? (
                                    <TouchableOpacity
                                        onPress={() => setShowSearch(true)}
                                        style={{
                                            flex: 1,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            backgroundColor: 'rgba(2 5,255,255,0.08)',
                                            borderRadius: 16,
                                            paddingHorizontal: 20,
                                            paddingVertical: 16,
                                            gap: 12,
                                            borderWidth: 1,
                                            borderColor: 'rgba(255,255,255,0.1)',
                                        }}
                                    >
                                        <Ionicons 
                                            name="search" 
                                            size={22} 
                                            color="rgba(255,255,255,0.6)" 
                                        />
                                        <Text color="rgba(255,255,255,0.6)" fontSize="$4" fontWeight="500">
                                            Search GIFs on Tenor
                                        </Text>
                                    </TouchableOpacity>
                                ) : (
                                    <Input
                                        placeholder="Search Tenor GIFs..."
                                        value={query}
                                        onChangeText={(text) => handleSearch(text, true)}
                                        flex={1}
                                        size="$4"
                                        backgroundColor="rgba(255,255,255,0.08)"
                                        borderWidth={1}
                                        borderColor="rgba(255,255,255,0.1)"
                                        borderRadius={16}
                                        paddingHorizontal="$4"
                                        autoFocus
                                        placeholderTextColor="rgba(255,255,255,0.4)"
                                        color="white"
                                    />
                                )}
                                
                                {(editMode || showSearch) && (
                                    <TouchableOpacity
                                        onPress={() => {
                                            setEditMode(false);
                                            setShowSearch(false);
                                            setQuery('');
                                            setResults([]);
                                            setFeaturedLoaded(false);
                                            setHasMore(false);
                                            setOffset(0);
                                        }}
                                        style={{
                                            paddingHorizontal: 18,
                                            paddingVertical: 10,
                                            backgroundColor: 'rgba(255,255,255,0.1)',
                                            borderRadius: 20,
                                        }}
                                    >
                                        <Text color="white" fontSize="$3" fontWeight="600">
                                            {editMode ? 'Done' : 'Close'}
                                        </Text>
                                    </TouchableOpacity>
                                )}
                            </XStack>
                                
                                {showSearch && (
                                    <>
                                        <ScrollView 
                                            showsVerticalScrollIndicator={false}
                                            contentContainerStyle={{ 
                                                paddingBottom: 16,
                                            }}
                                            onScroll={({ nativeEvent }) => {
                                                const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
                                                const paddingToBottom = 20;
                                                if (layoutMeasurement.height + contentOffset.y >= 
                                                    contentSize.height - paddingToBottom) {
                                                    loadMore();
                                                }
                                            }}
                                            scrollEventThrottle={400}
                                        >
                                            {loading && results.length === 0 ? (
                                                <YStack width="100%" height={140} justifyContent="center" alignItems="center">
                                                    <Text color="rgba(255,255,255,0.3)" fontSize="$2">Loading featured GIFs...</Text>
                                                </YStack>
                                            ) : (
                                                <YStack gap="$2">
                                                    <XStack flexWrap="wrap" gap="$2">
                                                        {results.map((item) => {
                                                            const previewUrl = item.media_formats?.tinygif?.url || 
                                                                             item.media_formats?.nanogif?.url || 
                                                                             item.media_formats?.mediumgif?.url || 
                                                                             item.media_formats?.gif?.url || '';
                                                            
                                                            if (!previewUrl) return null;
                                                            
                                                            const itemSize = (CANVAS_SIZE - 30) / 4; // 4 items per row with gaps
                                                            
                                                            return (
                                                                <TouchableOpacity
                                                                    key={item.id}
                                                                    onPress={() => handleSelectGif(item)}
                                                                    style={{
                                                                        width: itemSize,
                                                                        height: itemSize,
                                                                        borderRadius: 12,
                                                                        overflow: 'hidden',
                                                                        backgroundColor: 'rgba(255,255,255,0.03)',
                                                                    }}
                                                                >
                                                                    <Image
                                                                        source={{ uri: previewUrl }}
                                                                        style={{ 
                                                                            width: itemSize, 
                                                                            height: itemSize,
                                                                        }}
                                                                        resizeMode="cover"
                                                                    />
                                                                </TouchableOpacity>
                                                            );
                                                        })}
                                                    </XStack>
                                                    
                                                    {loading && results.length > 0 && (
                                                        <YStack width="100%" height={60} justifyContent="center" alignItems="center" marginTop="$2">
                                                            <Text color="rgba(255,255,255,0.5)" fontSize="$3">Loading more GIFs...</Text>
                                                        </YStack>
                                                    )}
                                                    
                                                    {!loading && hasMore && results.length > 0 && (
                                                        <YStack width="100%" height={40} justifyContent="center" alignItems="center" marginTop="$2">
                                                            <Text color="rgba(255,255,255,0.3)" fontSize="$2">Scroll for more</Text>
                                                        </YStack>
                                                    )}
                                                </YStack>
                                            )}
                                        </ScrollView>

                                        {!loading && results.length === 0 && query.length > 0 && (
                                            <YStack flex={1} justifyContent="center" alignItems="center">
                                                <Text color="rgba(255,255,255,0.3)" fontSize="$3">
                                                    {query.length > 2 ? 'No results' : 'Type to search...'}
                                                </Text>
                                            </YStack>
                                        )}
                                    </>
                                )}
                            </YStack>
                    </Stack>
                </KeyboardAvoidingView>
        </Stack>
    );
}