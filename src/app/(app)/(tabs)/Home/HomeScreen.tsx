import React, {useState} from "react";
import {ActivityIndicator, RefreshControl, ScrollView} from "react-native";
import {SafeAreaView} from "react-native-safe-area-context";
import FeedCard from "@/components/FeedCard/FeedCard";
import SortableSongList from "@/components/SortableSongList";
import {useQueryClient} from "@tanstack/react-query";
import {Button, Spacer, Text, XStack, YStack} from "tamagui";
import {useUserFeed} from "@/hooks/useUser";
import {invalidateQueries} from "@/utils/invalidateQueries";
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import {USER_FEED_QUERY_KEY, USER_LEVEL_QUERY_KEY} from "@/constants/queryKeys";
import {router} from "expo-router";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";

export default function HomeScreen() {
    const [refreshing, setRefreshing] = useState(false);
    const [showSortableSongList, setShowSortableSongList] = useState(false);
    const queryClient = useQueryClient();

    const {
        data: userFeedData,
        isLoading: isUserFeedLoading,
        isError: isUserFeedError,
        isSuccess: isUserFeedSuccess
    } = useUserFeed();

    const onRefresh = async () => {
        /**
         * This function will update all the necessary data when the user pulls down to refresh.
         */
        setRefreshing(true);
        await invalidateQueries({
            queryClient,
            queryKeys: [USER_FEED_QUERY_KEY, USER_LEVEL_QUERY_KEY],
        });
        setRefreshing(false);
    };

    const openRatingModal = () => {
        setShowSortableSongList(true);
    };

    const closeSortableSongList = () => {
        setShowSortableSongList(false);
    };

    if (isUserFeedLoading) {
        return (
            <YStack
                flex={1}
                justifyContent="center"
                alignItems="center"
            >
                <ActivityIndicator size="large" color="#fed900"/>
            </YStack>
        );
    }

    if (isUserFeedError) {
        return (
            <YStack
                flex={1}
                justifyContent="center"
                alignItems="center"
            >
                <Text>Error...</Text>
            </YStack>
        );
    }

    // TODO: Sort the post in the hook useUser?
    const sortedPosts = (userFeedData?.posts ?? []).sort((a, b) => new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime());

    // Show SortableSongList when state is true
    if (showSortableSongList) {
        return (
            <SafeAreaView style={{flex: 1}}>
                <XStack justifyContent="space-between" marginHorizontal={"$2"} paddingTop="$1">
                    <Button
                        size="$4"
                        backgroundColor="transparent"
                        onPress={closeSortableSongList}
                    >
                        <Text>← Back</Text>
                    </Button>
                </XStack>
                <SortableSongList />
            </SafeAreaView>
        );
    }

    return (
        <ScrollView
            style={{flex: 1}}
            showsVerticalScrollIndicator={false}
            refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh}/>
            }
        >
            <SafeAreaView>
                <XStack justifyContent="space-between" marginHorizontal={"$2"} paddingTop="$1">
                    <Text fontSize={30} fontWeight="bold" alignSelf="center">
                        Today's Bangers
                    </Text>
                    <XStack >
                        <Button
                            size="$5"
                            backgroundColor="transparent"
                            icon={<MaterialCommunityIcons name="collage" size={30} color="white" />}
                            onPress={() => router.push('/(app)/(tabs)/Home/MoodboardScreen')}
                        />
                        <Button
                            size="$5"
                            backgroundColor="transparent"
                            icon={<FontAwesome5 name="fire" size={30} color="white" />}
                            onPress={openRatingModal}
                        />
                    </XStack>
                </XStack>

                <YStack alignItems="center" gap="$2" paddingTop="$5">
                    {/*<PhaseProgressCard/>*/}
                    <Spacer size="$0.25"/>
                    {isUserFeedSuccess && sortedPosts.length === 0 ? (
                        <Text>No bangers yet from your friends</Text>
                    ) : (
                        sortedPosts.map((post, index) => (
                            <FeedCard
                                postID={post.postID}
                                key={index}
                                posterProfilePicture={post.posterProfilePicture}
                                posterUsername={post.posterUsername}
                                artworkURL={post.artworkURL}
                                artistName={post.artistName}
                                trackName={post.trackName}
                                postRating={post.postRating}
                                audioPreviewURL={post.audioPreviewURL}
                                spotifyURL={post.spotifyURL}
                                trackURL={post.trackURL}
                                userReaction={post.userReaction}
                                comments={post.comments}
                                audioCommentURL={post.audioCommentURL}
                                hideActionButtons={false}
                            />
                        ))
                    )}
                </YStack>
            </SafeAreaView>
            {/*<Leaderboard/>*/}
        </ScrollView>
    );
}