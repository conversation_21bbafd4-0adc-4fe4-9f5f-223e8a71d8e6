import React, { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { YStack, Text, Button } from 'tamagui';
import { router } from "expo-router";
import { requestTrackingPermissionsAsync } from "expo-tracking-transparency";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useUpdateTrackingPermission } from '@/hooks/usePermissions';

const TrackingPermissionScreen = () => {
    const [permissionStatus, setPermissionStatus] = useState<string>('unknown');
    const updateTrackingPermission = useUpdateTrackingPermission();

    useEffect(() => {
        // Only check current permission status, don't request it
        checkCurrentPermissionStatus();
    }, []);

    const checkCurrentPermissionStatus = async () => {
        try {
            const storedStatus = await AsyncStorage.getItem("tracking-permission");
            if (storedStatus) {
                setPermissionStatus(storedStatus);
            }
        } catch (error) {
            console.error('Error checking tracking permission status:', error);
        }
    };

    const requestPermission = async () => {
        try {
            const { status } = await requestTrackingPermissionsAsync();

            if (status === "granted") {
                console.log("Tracking Permission Granted");
                await AsyncStorage.setItem("tracking-permission", "granted");
                setPermissionStatus('granted');
                // Update permission status in backend
                await updateTrackingPermission.mutateAsync({ tracking: true });
                router.replace("/(app)/(onboarding)/LocationPermissionScreen");
            } else {
                console.log("Tracking Permission Denied");
                await AsyncStorage.setItem("tracking-permission", "denied");
                setPermissionStatus('denied');
                // Update permission status in backend
                await updateTrackingPermission.mutateAsync({ tracking: false });
                Alert.alert(
                    'Permission not granted',
                    'Allow tracking to help us provide personalized content and improve your experience.'
                );
            }
        } catch (error) {
            console.error('Error requesting tracking permission:', error);
            Alert.alert(
                'Error',
                'An error occurred while requesting permission. Please try again.'
            );
        }
    };

    return (
        <YStack flex={1} justifyContent="center" alignItems="center" padding="$4">
            <Text fontSize="$6" fontWeight="bold" marginBottom="$4">
                Enable App Tracking
            </Text>
            <Text fontSize="$4" textAlign="center" marginBottom="$6">
                Allow us to track your activity across apps and websites to provide you with personalized content and better recommendations.
            </Text>
            <Button
                size="$5"
                width="75%"
                onPress={requestPermission}
                marginBottom="$7"
                backgroundColor="#fed900"
                color={"black"}
            >
                Allow Tracking
            </Button>
            <Button
                size="$4"
                width="75%"
                onPress={async () => {
                    // Update permission status as skipped/denied
                    await updateTrackingPermission.mutateAsync({ permission: false });
                    router.replace("/(app)/(onboarding)/LocationPermissionScreen");
                }}
                chromeless
            >
                Skip
            </Button>
        </YStack>
    );
};

export default TrackingPermissionScreen;