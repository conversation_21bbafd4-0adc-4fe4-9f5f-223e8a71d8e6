import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Platform, Animated } from 'react-native';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { YStack, Text, Button, XStack, Card, AnimatePresence, View } from 'tamagui';
import { updatePNToken } from "@/api/usersAPI";
import { router } from "expo-router";
import { Bell, Music, Heart, Users, TrendingUp, Zap, Check } from '@tamagui/lucide-icons';
import { useUpdateNotificationPermission } from '@/hooks/usePermissions';

// Set up the notification handler
Notifications.setNotificationHandler({
    handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
        shouldShowBanner: true,
        shouldShowList: true,
    }),
});

const NotificationPermissionScreen = () => {
    const [pulseAnim] = useState(new Animated.Value(1));
    const [fadeAnim] = useState(new Animated.Value(0));
    const [skipVisible, setSkipVisible] = useState(false);
    const [benefitsVisible, setBenefitsVisible] = useState(false);

    useEffect(() => {
        // Only check current permission status, don't request it
        checkCurrentPermissionStatus();
        
        // Start animations
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
        }).start();

        // Pulse animation for the bell icon
        Animated.loop(
            Animated.sequence([
                Animated.timing(pulseAnim, {
                    toValue: 1.15,
                    duration: 1200,
                    useNativeDriver: true,
                }),
                Animated.timing(pulseAnim, {
                    toValue: 1,
                    duration: 1200,
                    useNativeDriver: true,
                }),
            ])
        ).start();

        // Show benefits after 500ms
        setTimeout(() => setBenefitsVisible(true), 500);

        // Show skip button after 3 seconds (psychological trick - makes them read the benefits first)
        const skipTimer = setTimeout(() => {
            setSkipVisible(true);
        }, 3000);

        return () => clearTimeout(skipTimer);
    }, []);

    const checkCurrentPermissionStatus = async () => {
        if (Device.isDevice) {
            const { status } = await Notifications.getPermissionsAsync();

            // If permission is already granted, get the token
            if (status === 'granted') {
                await getExistingPushToken();
            }
        }
    };

    const getExistingPushToken = async (): Promise<string | undefined> => {
        try {
            const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
            if (!projectId) {
                throw new Error('Project ID not found');
            }
            const token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
            return token;
        } catch (e) {
            console.error('Error getting push token:', e);
            return undefined;
        }
    };

    const requestPermission = async () => {
        const token = await registerForPushNotificationsAsync();
        if (token) {
            await updatePNToken(token);
            router.replace("/(app)/(onboarding)/TrackingPermissionScreen");
        } else {
            Alert.alert(
                '😢 Notifications Disabled',
                'You\'ll miss important updates from your friends!\n\nYou can always enable them later in:\nSettings > Notifications > WULLUP',
                [
                    { 
                        text: 'Continue Without', 
                        onPress: () => router.replace("/(app)/(onboarding)/TrackingPermissionScreen"),
                        style: 'cancel'
                    },
                    { 
                        text: 'Try Again', 
                        onPress: requestPermission 
                    }
                ]
            );
        }
    };

    const handleSkip = () => {
        Alert.alert(
            '🎵 Wait! You\'ll miss out on...',
            '• When friends share new songs\n• Music trending in your circle\n• Friend requests & messages\n• Battle results & challenges\n• Your weekly music stats\n\nAre you sure you want to skip?',
            [
                { 
                    text: 'Enable Notifications 🔔', 
                    onPress: requestPermission,
                    style: 'default'
                },
                { 
                    text: 'Skip Anyway', 
                    onPress: () => router.replace("/(app)/(onboarding)/TrackingPermissionScreen"),
                    style: 'destructive'
                }
            ]
        );
    };

    return (
        <YStack flex={1} backgroundColor="$background" padding="$4">
            <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
                <YStack flex={1} justifyContent="center" alignItems="center" gap="$4">
                    {/* Animated Bell Icon with Badges */}
                    <View position="relative" marginBottom="$6">
                        <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                            <Card
                                circular
                                size="$10"
                                backgroundColor="$yellow9"
                                justifyContent="center"
                                alignItems="center"
                                elevation="$4"
                                animation="bouncy"
                                pressStyle={{ scale: 0.95 }}
                            >
                                <Bell size={48} color="black" />
                            </Card>
                        </Animated.View>
                        
                        {/* Notification badges */}
                        <Card
                            circular
                            size="$2"
                            backgroundColor="$red9"
                            position="absolute"
                            top={-5}
                            right={-5}
                            justifyContent="center"
                            alignItems="center"
                        >
                            <Text fontSize="$1" color="white" fontWeight="bold">5</Text>
                        </Card>
                        
                        <Card
                            circular
                            size="$2"
                            backgroundColor="$pink9"
                            position="absolute"
                            bottom={0}
                            left={-5}
                            justifyContent="center"
                            alignItems="center"
                        >
                            <Heart size={12} color="white" />
                        </Card>
                        
                        <Card
                            circular
                            size="$2"
                            backgroundColor="$green9"
                            position="absolute"
                            top={10}
                            left={-10}
                            justifyContent="center"
                            alignItems="center"
                        >
                            <Music size={12} color="white" />
                        </Card>
                    </View>

                    {/* Title with emoji */}
                    <Text fontSize="$9" fontWeight="bold" textAlign="center" color="$color">
                        Stay in the Loop! 🎵
                    </Text>
                    
                    <Text fontSize="$5" textAlign="center" color="$gray11" marginHorizontal="$4">
                        Turn on notifications to never miss what's happening in your music circle
                    </Text>

                    {/* Benefits with icons - fade in with stagger */}
                    <AnimatePresence>
                        {benefitsVisible && (
                            <YStack gap="$3" marginTop="$4" width="100%" paddingHorizontal="$4">
                                {[
                                    { icon: Users, text: "Friend requests & new connections", color: "$blue9", delay: 100 },
                                    { icon: Music, text: "New songs from your friends", color: "$purple9", delay: 200 },
                                    { icon: TrendingUp, text: "Trending music in your circle", color: "$orange9", delay: 300 },
                                    { icon: Heart, text: "When someone likes your taste", color: "$pink9", delay: 400 },
                                    { icon: Zap, text: "Music battle challenges", color: "$yellow9", delay: 500 },
                                ].map((benefit, index) => (
                                    <XStack
                                        key={index}
                                        gap="$3"
                                        alignItems="center"
                                        animation="quick"
                                        enterStyle={{ opacity: 0, x: -20 }}
                                        exitStyle={{ opacity: 0, x: 20 }}
                                        animateOnly={["transform", "opacity"]}
                                    >
                                        <Card
                                            circular
                                            size="$3"
                                            backgroundColor={benefit.color}
                                            opacity={0.2}
                                            justifyContent="center"
                                            alignItems="center"
                                        >
                                            <benefit.icon size={20} color={benefit.color} />
                                        </Card>
                                        <Text flex={1} fontSize="$4" color="$color">
                                            {benefit.text}
                                        </Text>
                                        <Check size={16} color="$green9" />
                                    </XStack>
                                ))}
                            </YStack>
                        )}
                    </AnimatePresence>

                    {/* Social Proof */}
                    <Card
                        backgroundColor="$gray3"
                        paddingVertical="$3"
                        paddingHorizontal="$4"
                        borderRadius="$4"
                        marginTop="$4"
                    >
                        <Text fontSize="$3" textAlign="center" color="$gray11">
                            <Text fontWeight="bold" color="$yellow9">93% of users</Text> enable notifications
                            {'\n'}for the best WULLUP experience
                        </Text>
                    </Card>
                </YStack>

                {/* Buttons */}
                <YStack gap="$3" paddingHorizontal="$2">
                    <Button
                        size="$5"
                        backgroundColor="$yellow9"
                        color="black"
                        onPress={requestPermission}
                        fontWeight="bold"
                        fontSize="$5"
                        pressStyle={{ scale: 0.98 }}
                        animation="quick"
                        icon={<Bell size={20} />}
                    >
                        Yes! Enable Notifications
                    </Button>
                    
                    <AnimatePresence>
                        {skipVisible && (
                            <Button
                                size="$3"
                                chromeless
                                onPress={handleSkip}
                                animation="quick"
                                enterStyle={{ opacity: 0, y: 10 }}
                                exitStyle={{ opacity: 0, y: -10 }}
                                animateOnly={["transform", "opacity"]}
                                opacity={0.6}
                            >
                                Maybe later
                            </Button>
                        )}
                    </AnimatePresence>
                </YStack>
            </Animated.View>
        </YStack>
    );
};

async function registerForPushNotificationsAsync(): Promise<string | undefined> {
    let token: string | undefined;

    if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
            name: 'default',
            importance: Notifications.AndroidImportance.MAX,
            vibrationPattern: [0, 250, 250, 250],
            lightColor: '#FF231F7C',
        });
    }

    if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        // Only request permission if it hasn't been granted yet
        if (existingStatus !== 'granted') {
            const { status } = await Notifications.requestPermissionsAsync();
            finalStatus = status;
        }

        if (finalStatus !== 'granted') {
            return undefined;
        }

        try {
            const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
            if (!projectId) {
                throw new Error('Project ID not found');
            }
            token = (await Notifications.getExpoPushTokenAsync({ projectId })).data;
        } catch (e) {
            console.error('Error getting push token:', e);
            return undefined;
        }
    } else {
        Alert.alert('Error', 'Must use physical device for Push Notifications');
    }

    return token;
}

export default NotificationPermissionScreen;