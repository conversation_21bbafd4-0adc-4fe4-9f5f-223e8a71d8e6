import React, { useState } from 'react';
import {
    YStack,
    Button,
    Spacer,
    H2,
    H6,
    Avatar
} from "tamagui";
import { SafeAreaView } from "react-native-safe-area-context";
import { KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from "expo-router";
import * as ImagePicker from 'expo-image-picker';
import { useMutation } from "@tanstack/react-query";
import { uploadImage } from "@/api/usersAPI";
import { AxiosError } from "axios";
import { useUpdateLibraryPermission } from '@/hooks/usePermissions';

export default function ProfilePictureScreen() {
    const [profileImage, setProfileImage] = useState<string | null>(null);
    const updateLibraryPermission = useUpdateLibraryPermission();

    const uploadImageMutation = useMutation({
        mutationFn: uploadImage,
        onSuccess: () => {
            // console.log("Image Upload Successful")
        },
        onError: (err: AxiosError) => {
            console.log(err)
            Alert.alert("Error", "An error occurred while uploading the image. Please try again later.");
        }
    });

    const pickImage = async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        
        // Update permission status in backend
        await updateLibraryPermission.mutateAsync({ permission: status === 'granted' });
        
        if (status !== 'granted') {
            Alert.alert('Permission denied', 'We need permission to access your photo library.');
            return;
        }

        let result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 1,
        });

        if (!result.canceled) {
            const uri = result.assets[0].uri;
            setProfileImage(uri);
            uploadImageMutation.mutate({ uri });
        }
    };

    const handleNext = () => {
        router.push("/(app)/(onboarding)/UsernameScreen");
    };

    const handleSkip = () => {
        router.push("/(app)/(onboarding)/UsernameScreen");
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}
            >
                <YStack
                    width="100%"
                    alignItems="center"
                    paddingTop={50}
                    space
                >
                    <H2 textAlign="center">Add a profile picture</H2>
                    <H6 opacity={0.5}>Step 5 of 5</H6>

                    <YStack alignItems="center" justifyContent="center" paddingTop={40}>
                        <YStack position="relative" width={200} height={200} alignItems="center">
                            <Avatar circular size="$15">
                                {profileImage ? (
                                    <Avatar.Image
                                        accessibilityLabel="Profile Picture"
                                        src={profileImage}
                                    />
                                ) : (
                                    <Avatar.Fallback backgroundColor="$yellow10" />
                                )}
                            </Avatar>
                            <Button
                                size="$6"
                                circular
                                position="absolute"
                                bottom={1}
                                right={1}
                                backgroundColor="white"
                                borderRadius={25}
                                padding={8}
                                onPress={pickImage}
                            >
                                <Ionicons name="camera" size={24} color="black" />
                            </Button>
                        </YStack>
                    </YStack>

                    <Spacer size="$6" />
                </YStack>

                <YStack position="absolute" bottom={20} width="100%" space="$3">
                    <Button
                        onPress={handleNext}
                        width="100%"
                        size="$5"
                        backgroundColor={profileImage ? '#fed900' : '#fed900'}
                        color="black"
                        disabled={uploadImageMutation.isPending}
                    >
                        {uploadImageMutation.isPending ? 'Uploading...' : 'Continue'}
                    </Button>

                    <Button
                        onPress={handleSkip}
                        width="100%"
                        size="$5"
                        backgroundColor="transparent"
                        color="$gray10"
                        borderWidth={0}
                    >
                        Skip for now
                    </Button>
                </YStack>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
}
