import React, { useState, useEffect } from 'react';
import { Al<PERSON>, Platform, Linking } from 'react-native';
import { YStack, Text, Button } from 'tamagui';
import { router } from "expo-router";
import * as Location from 'expo-location';
import AsyncStorage from "@react-native-async-storage/async-storage";
import {updateUserLocation, useUpdateLocationPermission} from '@/hooks/usePermissions';

const LocationPermissionScreen = () => {
    const [permissionStatus, setPermissionStatus] = useState<Location.PermissionStatus | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const updateLocationPermission = useUpdateLocationPermission();

    useEffect(() => {
        checkCurrentPermissionStatus();
    }, []);

    const checkCurrentPermissionStatus = async () => {
        try {
            // Only check foreground permissions (When In Use)
            const { status } = await Location.getForegroundPermissionsAsync();
            setPermissionStatus(status);
            await AsyncStorage.setItem("location-permission", status);
        } catch (error) {
            console.error('Error checking location permission status:', error);
            setPermissionStatus(Location.PermissionStatus.DENIED);
        }
    };

    const requestWhenInUsePermission = async () => {
        setIsLoading(true);

        try {
            // Request only foreground permissions (When In Use)
            const { status } = await Location.requestForegroundPermissionsAsync();

            setPermissionStatus(status);

            if (status === Location.PermissionStatus.GRANTED) {
                console.log("When In Use Location Permission Granted");
                await AsyncStorage.setItem("location-permission", "granted");

                // Get and log the user's current location
                try {
                    const location = await Location.getCurrentPositionAsync({
                        accuracy: Location.Accuracy.High,
                    });
                    console.log("User's current location:", location);
                } catch (locationError) {
                    console.error("Error getting current location:", locationError);
                }
                
                // Update permission status in backend
                await updateLocationPermission.mutateAsync({ location: true });
                await updateUserLocation.mutateAsync(
                    {
                        latitude: location.coords.latitude,
                        longitude: location.coords.longitude,
                        accuracy: location.coords.accuracy,
                        altitude: location.coords.altitude,
                        heading: location.coords.heading,
                        speed: location.coords.speed
                    }
                );

                // Navigate to home screen
                router.replace("/(app)/(tabs)/Home/HomeScreen");
            } else {
                console.log("When In Use Location Permission Denied");
                await AsyncStorage.setItem("location-permission", "denied");
                
                // Update permission status in backend
                await updateLocationPermission.mutateAsync({ location: false });

                showPermissionDeniedAlert();
            }
        } catch (error) {
            console.error('Error requesting when-in-use location permission:', error);
            Alert.alert(
                'Error',
                'An error occurred while requesting location permission. Please try again.'
            );
        } finally {
            setIsLoading(false);
        }
    };

    const showPermissionDeniedAlert = () => {
        Alert.alert(
            'Location Access Required',
            'Wullup needs location access to discover nearby friends and provide personalized content. You can enable this in Settings.',
            [
                {
                    text: 'Cancel',
                    style: 'cancel'
                },
                {
                    text: 'Open Settings',
                    onPress: () => {
                        Linking.openSettings();
                    }
                }
            ]
        );
    };

    const skipLocationSetup = async () => {
        await AsyncStorage.setItem("location-permission", "skipped");
        // Update permission status as skipped/denied
        await updateLocationPermission.mutateAsync({ permission: false });
        router.replace("/(app)/(tabs)/Home/HomeScreen");
    };

    return (
        <YStack flex={1} justifyContent="center" alignItems="center" padding="$4">
            <Text fontSize="$6" fontWeight="bold" marginBottom="$4">
                Enable Location Services
            </Text>

            <Text fontSize="$4" textAlign="center" marginBottom="$6" color="$gray11">
                Allow Wullup to use your location to discover nearby friends, get personalized recommendations, and enhance your experience.
            </Text>

            {permissionStatus === Location.PermissionStatus.DENIED && (
                <Text fontSize="$3" textAlign="center" marginBottom="$4" color="$red10">
                    Location access is currently denied. Tap "Allow Location Access" to enable.
                </Text>
            )}

            <Button
                size="$5"
                width="75%"
                onPress={requestWhenInUsePermission}
                marginBottom="$4"
                backgroundColor="#fed900"
                color="black"
                disabled={isLoading}
                opacity={isLoading ? 0.6 : 1}
            >
                {isLoading ? "Requesting Permission..." : "Allow Location Access"}
            </Button>

            <Button
                size="$4"
                width="75%"
                onPress={skipLocationSetup}
                chromeless
                disabled={isLoading}
            >
                Skip for Now
            </Button>
        </YStack>
    );
};

export default LocationPermissionScreen;
