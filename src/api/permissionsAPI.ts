import {api} from './apiConfig'

export const updateTrackingPermission = async (tracking: boolean): Promise<any> => {
    const result = await api.put('/v1/permissions/tracking', {tracking});
    return result.data;
}

export const updateLocationPermission = async (location: boolean): Promise<any> => {
    const result = await api.put('/v1/permissions/location', {location});
    return result.data;
}

export const updateLocation = async (location: {latitude: any, longitude: any, accuracy: any, altitude: any, heading: any, speed: any}): Promise<any> => {
    const result = await api.post('/v1/permissions/location', location);
    return result.data;
}

export const updatePushNotificationsPermission = async (pushNotifications: boolean, expoPushToken?: string): Promise<any> => {
    const result = await api.put('/v1/permissions/push-notifications', {pushNotifications, expoPushToken});
    return result.data;
}

export const updateMicrophonePermission = async (permission: boolean): Promise<any> => {
    const result = await api.put('/v1/permissions/microphone', {permission});
    return result.data;
}

export const updateLibraryPermission = async (permission: boolean): Promise<any> => {
    const result = await api.put('/v1/permissions/library', {permission});
    return result.data;
}

export const updateContactsPermission = async (permission: boolean): Promise<any> => {
    const result = await api.put('/v1/permissions/contacts', {permission});
    return result.data;
}

