import { useMutation, useQueryClient } from '@tanstack/react-query';
import {useToastController} from "@tamagui/toast";
import {
    updateContactsPermission, updateLibraryPermission, updateLocation,
    updateLocationPermission, updateMicrophonePermission,
    updatePushNotificationsPermission,
    updateTrackingPermission
} from "@/api/permissionsAPI";



export const useUpdateTrackingPermission = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();

    return useMutation({
        mutationFn: async ({ tracking }: { tracking: boolean }) =>
            updateTrackingPermission(tracking),
        onError: (error: any) => {
            console.log("Error updating tracking permission:", error.message || error);
            toast.show('Error', {
                message: "Failed to update tracking permission",
                theme: 'red',
            });
        },
    });
}


export const useUpdateUserLocation = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();
    return useMutation({
        mutationFn: async ({
                               latitude,
                               longitude,
                               accuracy,
                               altitude,
                               heading,
                               speed
                           }: {
            latitude: any;
            longitude: any;
            accuracy: any;
            altitude: any;
            heading: any;
            speed: any
        }) => updateLocation({ latitude, longitude, accuracy, altitude, heading, speed }),
        onError: (error: any) => {
            console.log("Error updating location:", error.message || error);
            toast.show('Error', {
                message: "Failed to update location",
                theme: 'red',
            });
        },
    });
}


export const useUpdateNotificationPermission = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();
    return useMutation({
        mutationFn: async ({ pushNotifications, expoPushToken }: { pushNotifications: boolean; expoPushToken?: string }) =>
            updatePushNotificationsPermission(pushNotifications, expoPushToken),
        onError: (error: any) => {
            console.log("Error updating notification permission:", error.message || error);
            toast.show('Error', {
                message: "Failed to update notification permission",
                theme: 'red',
            });
        },
    });
}


export const useUpdateLocationPermission = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();
    return useMutation({
        mutationFn: async ({ location }: { location: boolean }) =>
            updateLocationPermission(location),
        onError: (error: any) => {
            console.log("Error updating location permission:", error.message || error);
            toast.show('Error', {
                message: "Failed to update location permission",
                theme: 'red',
            });
        },
    });
}

export const useUpdateMicrophonePermission = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();
    return useMutation({
        mutationFn: async ({ permission }: { permission: boolean }) =>
            updateMicrophonePermission(permission),
        onError: (error: any) => {
            console.log("Error updating microphone permission:", error.message || error);
            toast.show('Error', {
                message: "Failed to update microphone permission",
                theme: 'red',
            });
        },
    });
}

export const useUpdateLibraryPermission = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();
    return useMutation({
        mutationFn: async ({ permission }: { permission: boolean }) =>
            updateLibraryPermission(permission),
        onError: (error: any) => {
            console.log("Error updating library permission:", error.message || error);
            toast.show('Error', {
                message: "Failed to update library permission",
                theme: 'red',
            });
        },
    });
}

export const useUpdateContactsPermission = () => {
    const queryClient = useQueryClient();
    const toast = useToastController();
    return useMutation({
        mutationFn: async ({ permission }: { permission: boolean }) =>
            updateContactsPermission(permission),
        onError: (error: any) => {
            console.log("Error updating contacts permission:", error.message || error);
            toast.show('Error', {
                message: "Failed to update contacts permission",
                theme: 'red',
            });
        },
    });
}